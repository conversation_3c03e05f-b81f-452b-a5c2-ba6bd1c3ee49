import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
  Avatar,
} from '@mui/material';
import {
  School as SchoolIcon,
  Person as PersonIcon,
  Work as WorkIcon,
} from '@mui/icons-material';

const RegistroSeleccionPage: React.FC = () => {
  const navigate = useNavigate();

  const registroOptions = [
    {
      title: 'Registro de Alumno',
      description: 'Registrar un nuevo estudiante en el sistema',
      icon: <SchoolIcon sx={{ fontSize: 60 }} />,
      color: '#1976d2',
      path: '/registro-alumno',
    },
    {
      title: 'Registro de Docente',
      description: 'Registrar un nuevo docente en el sistema',
      icon: <PersonIcon sx={{ fontSize: 60 }} />,
      color: '#388e3c',
      path: '/registro-docente',
    },
    {
      title: 'Registro de Personal',
      description: 'Registrar nuevo personal administrativo',
      icon: <WorkIcon sx={{ fontSize: 60 }} />,
      color: '#f57c00',
      path: '/registro-personal',
    },
  ];

  const handleOptionClick = (path: string) => {
    navigate(path);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h3" component="h1" gutterBottom>
            Seleccionar Tipo de Registro
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Elige el tipo de persona que deseas registrar en el sistema
          </Typography>
        </Box>

        <Grid container spacing={4} justifyContent="center">
          {registroOptions.map((option, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
                onClick={() => handleOptionClick(option.path)}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                  <Typography variant="h5" component="h2" gutterBottom>
                    {option.title}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {option.description}
                  </Typography>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', pb: 3 }}>
                  <Button
                    variant="contained"
                    size="large"
                    sx={{ bgcolor: option.color }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOptionClick(option.path);
                    }}
                  >
                    Seleccionar
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box textAlign="center" mt={4}>
          <Button
            variant="outlined"
            size="large"
            onClick={() => navigate('/login')}
          >
            Volver al Login
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default RegistroSeleccionPage;
