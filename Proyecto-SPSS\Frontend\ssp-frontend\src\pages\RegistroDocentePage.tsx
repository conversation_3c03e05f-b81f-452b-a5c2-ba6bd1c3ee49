import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import PersonaForm from '@/components/PersonaForm';
import { personasApi, gruposApi, programasEducativosApi } from '@/services/api';
import type { Persona } from '@/types';
import type { Grupo } from '@/types';
import type { ProgramaEducativo } from '@/types';

const RegistroDocentePage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [grupos, setGrupos] = useState<Grupo[]>([]);
  const [programas, setProgramas] = useState<ProgramaEducativo[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [gruposResponse, programasResponse] = await Promise.all([
          gruposApi.getAll(),
          programasEducativosApi.getAll(),
        ]);
        setGrupos(gruposResponse.data);
        setProgramas(programasResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Error al cargar los datos necesarios');
      }
    };

    fetchData();
  }, []);

  const handleSubmit = async (personaData: Omit<Persona, 'id'>) => {
    setLoading(true);
    setError(null);

    try {
      // Crear la persona con rol de docente
      const docenteData = {
        ...personaData,
        rol: 'docente' as const,
      };

      await personasApi.create(docenteData);
      setSuccess(true);
      
      // Redirigir después de un breve delay
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (error: any) {
      console.error('Error creating docente:', error);
      setError(error.response?.data?.detail || 'Error al registrar el docente');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/registro');
  };

  if (success) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box textAlign="center">
            <Alert severity="success" sx={{ mb: 2 }}>
              ¡Docente registrado exitosamente!
            </Alert>
            <Typography variant="h6">
              Redirigiendo al login...
            </Typography>
          </Box>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Registro de Docente
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Complete la información para registrar un nuevo docente
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <PersonaForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
          grupos={grupos}
          programas={programas}
          rol="docente"
          allowMultipleGroups={true}
        />

        {loading && (
          <Box display="flex" justifyContent="center" mt={2}>
            <CircularProgress />
          </Box>
        )}

        <Box textAlign="center" mt={4}>
          <Button
            variant="outlined"
            onClick={() => navigate('/registro')}
            disabled={loading}
          >
            Volver a Selección
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default RegistroDocentePage;
